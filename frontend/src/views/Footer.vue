<script setup>
import { useI18n } from 'vue-i18n'
import { useGlobalState } from '../store'
const { openSettings } = useGlobalState()


const { t } = useI18n({
    messages: {
        en: {
            copyright: "Copyright"
        },
        zh: {
            copyright: "版权所有"
        }
    }
});

</script>

<template>
    <div>
        <n-divider class="footer-divider" />
        <div style="text-align: center; padding: 20px">
            <n-space justify="center">
                <n-text depth="3">
                    {{ t('copyright') }} © 2023-2025 Dream Hunter
                </n-text>
                <n-text depth="3">
                    <div v-html="openSettings.copyright"></div>
                </n-text>
            </n-space>
        </div>
    </div>
</template>


<style scoped>
.footer-divider {
    margin: 0;
    padding: 0 var(--x-padding);
    opacity: 0.3;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Footer 整体样式优化 */
div {
    position: relative;
    margin-top: auto;
}

/* 版权信息样式 */
div[style*="text-align: center"] {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
    margin-top: 40px;
    position: relative;
    overflow: hidden;
}

div[style*="text-align: center"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}

.n-text {
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.n-text:hover {
    transform: translateY(-1px);
}

/* 暗色主题适配 */
[data-theme="dark"] div[style*="text-align: center"] {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
}

[data-theme="dark"] div[style*="text-align: center"]::before {
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    div[style*="text-align: center"] {
        margin: 20px 16px 0;
        border-radius: 12px;
    }

    .n-space {
        flex-direction: column;
        gap: 8px;
    }
}
</style>
