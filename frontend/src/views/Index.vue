<script setup>
import { defineAsyncComponent, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

import { useGlobalState } from '../store'
import { api } from '../api'

import AddressBar from './index/AddressBar.vue';
import MailBox from '../components/MailBox.vue';
import SendBox from '../components/SendBox.vue';
import AutoReply from './index/AutoReply.vue';
import AccountSettings from './index/AccountSettings.vue';
import Webhook from './index/Webhook.vue';
import Attachment from './index/Attachment.vue';
import About from './common/About.vue';

const { loading, settings, openSettings, indexTab, globalTabplacement } = useGlobalState()
const message = useMessage()
const route = useRoute()

const SendMail = defineAsyncComponent(() => {
  loading.value = true;
  return import('./index/SendMail.vue')
    .finally(() => loading.value = false);
});

const { t } = useI18n({
  messages: {
    en: {
      mailbox: 'Mail Box',
      sendbox: 'Send Box',
      sendmail: 'Send Mail',
      auto_reply: 'Auto Reply',
      accountSettings: 'Account Settings',
      about: 'About',
      s3Attachment: 'S3 Attachment',
      saveToS3Success: 'save to s3 success',
      webhookSettings: 'Webhook Settings',
      query: 'Query',
    },
    zh: {
      mailbox: '收件箱',
      sendbox: '发件箱',
      sendmail: '发送邮件',
      auto_reply: '自动回复',
      accountSettings: '账户设置',
      about: '关于',
      s3Attachment: 'S3附件',
      saveToS3Success: '保存到s3成功',
      webhookSettings: 'Webhook 设置',
      query: '查询',
    }
  }
});

const fetchMailData = async (limit, offset) => {
  if (mailIdQuery.value > 0) {
    const singleMail = await api.fetch(`/api/mail/${mailIdQuery.value}`);
    if (singleMail) return { results: [singleMail], count: 1 };
    return { results: [], count: 0 };
  }
  return await api.fetch(`/api/mails?limit=${limit}&offset=${offset}`);
};

const deleteMail = async (curMailId) => {
  await api.fetch(`/api/mails/${curMailId}`, { method: 'DELETE' });
};

const deleteSenboxMail = async (curMailId) => {
  await api.fetch(`/api/sendbox/${curMailId}`, { method: 'DELETE' });
};

const fetchSenboxData = async (limit, offset) => {
  return await api.fetch(`/api/sendbox?limit=${limit}&offset=${offset}`);
};

const saveToS3 = async (mail_id, filename, blob) => {
  try {
    const { url } = await api.fetch(`/api/attachment/put_url`, {
      method: 'POST',
      body: JSON.stringify({ key: `${mail_id}/${filename}` })
    });
    // upload to s3 by formdata
    const formData = new FormData();
    formData.append(filename, blob);
    await fetch(url, {
      method: 'PUT',
      body: formData
    });
    message.success(t('saveToS3Success'));
  } catch (error) {
    console.error(error);
    message.error(error.message || "save to s3 error");
  }
}

const mailBoxKey = ref("")
const mailIdQuery = ref("")
const showMailIdQuery = ref(false)

const queryMail = () => {
  mailBoxKey.value = Date.now();
}

watch(route, () => {
  if (!route.query.mail_id) {
    showMailIdQuery.value = false;
    mailIdQuery.value = "";
    queryMail();
  }
})

onMounted(() => {
  if (route.query.mail_id) {
    showMailIdQuery.value = true;
    mailIdQuery.value = route.query.mail_id;
    queryMail();
  }
})
</script>

<template>
  <div class="index-container">
    <AddressBar />
    <n-tabs v-if="settings.address" type="card" v-model:value="indexTab" :placement="globalTabplacement" class="main-tabs">
      <n-tab-pane name="mailbox" :tab="t('mailbox')">
        <div v-if="showMailIdQuery" class="query-section">
          <n-input-group>
            <n-input v-model:value="mailIdQuery" placeholder="输入邮件ID进行查询..." />
            <n-button @click="queryMail" type="primary" tertiary>
              {{ t('query') }}
            </n-button>
          </n-input-group>
        </div>
        <MailBox :key="mailBoxKey" :showEMailTo="false" :showReply="true" :showSaveS3="openSettings.isS3Enabled"
          :saveToS3="saveToS3" :enableUserDeleteEmail="openSettings.enableUserDeleteEmail"
          :fetchMailData="fetchMailData" :deleteMail="deleteMail" />
      </n-tab-pane>
      <n-tab-pane name="sendbox" :tab="t('sendbox')">
        <SendBox :fetchMailData="fetchSenboxData" :enableUserDeleteEmail="openSettings.enableUserDeleteEmail"
          :deleteMail="deleteSenboxMail" />
      </n-tab-pane>
      <n-tab-pane name="sendmail" :tab="t('sendmail')">
        <SendMail />
      </n-tab-pane>
      <n-tab-pane name="accountSettings" :tab="t('accountSettings')">
        <AccountSettings />
      </n-tab-pane>
      <n-tab-pane v-if="openSettings.enableAutoReply" name="auto_reply" :tab="t('auto_reply')">
        <AutoReply />
      </n-tab-pane>
      <n-tab-pane v-if="openSettings.enableWebhook" name="webhook" :tab="t('webhookSettings')">
        <Webhook />
      </n-tab-pane>
      <n-tab-pane v-if="openSettings.isS3Enabled" name="s3_attachment" :tab="t('s3Attachment')">
        <Attachment />
      </n-tab-pane>
      <n-tab-pane v-if="openSettings.enableIndexAbout" name="about" :tab="t('about')">
        <About />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<style scoped>
.index-container {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-tabs {
  margin-top: 20px;
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-tabs :deep(.n-tabs-nav) {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 12px;
  margin: 0 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-tabs :deep(.n-tabs-tab) {
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
}

.main-tabs :deep(.n-tabs-tab:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.main-tabs :deep(.n-tabs-tab--active) {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.main-tabs :deep(.n-tabs-pane) {
  padding: 20px 0;
  animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.query-section {
  margin-bottom: 20px;
  animation: slideInDown 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.query-section .n-input-group {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.query-section .n-input-group:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.query-section .n-input {
  border: none;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  color: #ffffff;
}

.query-section .n-button {
  border-radius: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.query-section .n-button:hover {
  transform: scale(1.05);
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 暗色主题适配 */
html.dark .main-tabs :deep(.n-tabs-nav) {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

html.dark .main-tabs :deep(.n-tabs-tab--active) {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

html.dark .query-section .n-input {
  background: rgba(0, 0, 0, 0.4);
  color: #f8fafc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .index-container {
    padding: 0 16px;
  }

  .main-tabs :deep(.n-tabs-nav) {
    padding: 6px;
  }

  .query-section {
    margin-bottom: 16px;
  }
}
</style>
